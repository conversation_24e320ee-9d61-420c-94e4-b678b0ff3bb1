package com.gg.gapo.messenger.presentation.features.settings.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.messenger.databinding.FragmentGroupChatSettingsMemberOptionsBinding
import com.gg.gapo.messenger.domain.models.User
import com.gg.gapo.messenger.presentation.features.managers.ChatACL
import org.koin.android.ext.android.inject

class MemberOptionsBottomSheetDialogFragment : GapoBottomSheetFragment() {

    private var bottomSheetDialogListener: MemberOptionsBottomSheetDialogListener? = null
    private var binding by autoCleared<FragmentGroupChatSettingsMemberOptionsBinding>()
    private val acl by inject<ChatACL>()
    private val workspaceManager by inject<WorkspaceManager>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentGroupChatSettingsMemberOptionsBinding.inflate(inflater, container, false)
        val data = arguments?.getSerializable(ARGUMENT_USER) as User?
        val threadId = arguments?.getString(ARGUMENT_THREAD).orEmpty()

        binding.threadId = threadId
        binding.user = data
        binding.isFeatureMessengerEnabled = workspaceManager.currentWorkspace?.features?.isEnable(Feature.MESSENGER) ?: true
        binding.isFeatureCallEnabled = workspaceManager.currentWorkspace?.features?.isEnable(Feature.CALL) ?: true
        binding.acl = acl
        binding.textTitle.text = data?.name.orEmpty()
        binding.blockViewProfile.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onViewProfile(binding.root.context, data)
                dismissAllowingStateLoss()
            }
        }
        binding.blockChat.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onChat(binding.root.context, data)
                dismissAllowingStateLoss()
            }
        }
        binding.blockRemove.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onRemoveUser(binding.root.context, data)
                dismissAllowingStateLoss()
            }
        }

        binding.assignAdmin.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onAssignAdmin(binding.root.context, data)
                dismissAllowingStateLoss()
            }
        }

        binding.removeAdmin.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onRemoveAdmin(binding.root.context, data)
                dismissAllowingStateLoss()
            }
        }

        binding.assignOwner.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onAssignOwner(binding.root.context, data)
                dismissAllowingStateLoss()
            }
        }
        binding.textViewCommonGroups.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onViewCommonGroups(binding.root.context, data)
                dismissAllowingStateLoss()
            }
        }
        binding.textViewAudioCall.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onAudioCall(data)
                dismissAllowingStateLoss()
            }
        }
        binding.textViewVideoCall.setDebouncedClickListener {
            data?.let {
                bottomSheetDialogListener?.onVideoCall(data)
                dismissAllowingStateLoss()
            }
        }
        return binding.root
    }

    fun setOnMemberOptionsBottomSheetListener(bottomSheetDialogListener: MemberOptionsBottomSheetDialogListener?) {
        this.bottomSheetDialogListener = bottomSheetDialogListener
    }

    companion object {

        fun show(
            manager: FragmentManager,
            isDisableGroup: Boolean = false,
            user: User,
            threadId: String,
            listener: MemberOptionsBottomSheetDialogListener
        ) {
            val ft = manager.beginTransaction()
            ft.add(
                MemberOptionsBottomSheetDialogFragment().apply {
                    arguments = Bundle().apply {
                        putSerializable(
                            ARGUMENT_USER,
                            user
                        )
                        putBoolean(ARGUMENT_DISABLE_GROUP, isDisableGroup)
                        putString(ARGUMENT_THREAD, threadId)
                    }
                    setOnMemberOptionsBottomSheetListener(listener)
                },
                "MemberOptionsBottomSheetDialogFragment"
            )
            ft.commitAllowingStateLoss()
        }

        private const val ARGUMENT_USER = "ARGUMENT_USER"

        private const val ARGUMENT_DISABLE_GROUP = "DISABLE_GROUP"

        private const val ARGUMENT_THREAD = "THREAD"
    }

    interface MemberOptionsBottomSheetDialogListener {

        fun onViewProfile(context: Context, user: User)

        fun onChat(context: Context, user: User)

        fun onRemoveUser(context: Context, user: User)

        fun onAssignAdmin(context: Context, user: User)

        fun onRemoveAdmin(context: Context, user: User)

        fun onAssignOwner(context: Context, user: User)

        fun onViewCommonGroups(context: Context, user: User)

        fun onAudioCall(user: User)

        fun onVideoCall(user: User)
    }
}
