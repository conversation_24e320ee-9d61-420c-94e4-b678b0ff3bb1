package com.gg.gapo.messenger.presentation.features.create.group.models

import android.content.Context
import android.view.View
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.gg.gapo.core.ui.GapoAutoDimens
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.ItemMessengerUserSelectionVerticalBinding
import com.gg.gapo.messenger.domain.models.User
import com.gg.gapo.messenger.helper.extensions.load
import com.gg.gapo.messenger.helper.extensions.loadCircleUserAvatar
import com.gg.gapo.messenger.presentation.bases.DataBindingModel
import com.gg.gapo.messenger.presentation.features.folder.selection.SelectionListener

@EpoxyModelClass
abstract class GroupUserSelectionVerticalModel : DataBindingModel<ItemMessengerUserSelectionVerticalBinding>() {

    @EpoxyAttribute
    var user: User? = null

    @EpoxyAttribute
    var selected: Boolean = false

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var handler: SelectionListener? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var showDivider: Boolean = true

    override fun bind(binding: ItemMessengerUserSelectionVerticalBinding, context: Context) {
        user?.let {
            binding.ivmAvatar.loadCircleUserAvatar(it, GapoAutoDimens._64dp)
            binding.checkBox.setOnCheckedChangeListener(null)
            if (selected != binding.checkBox.isChecked) {
                binding.checkBox.isChecked = selected
            }
            binding.checkBox.setOnCheckedChangeListener { view, isCheck ->
                handler?.onSelected(it, isCheck)
            }
            binding.badge.visibility = if (it.isOwner() || it.isAdmin()) {
                if (it.isOwner()) {
                    binding.badge.load(R.drawable.ic_group_chat_settings_owner_badge)
                } else {
                    binding.badge.load(R.drawable.ic_group_chat_settings_admin_badge)
                }
                View.VISIBLE
            } else View.GONE

            binding.root.setDebouncedClickListener { view ->
                binding.checkBox.isChecked = !binding.checkBox.isChecked
            }

            binding.divider.visibility = if (showDivider) {
                View.VISIBLE
            } else View.GONE

            binding.tvName.text = it.getUserName()
            if (it.company.isNotEmpty()) {
                binding.tvCompany.text = it.company
                binding.tvCompany.visibility = View.VISIBLE
                binding.tvCompanyIcon.visibility = View.VISIBLE
            } else {
                binding.tvCompany.visibility = View.GONE
                binding.tvCompanyIcon.visibility = View.GONE
            }
            if (it.position.isNotEmpty()) {
                binding.tvPosition.text = it.position
                binding.tvPosition.visibility = View.VISIBLE
                binding.tvPositionIcon.visibility = View.VISIBLE
            } else {
                binding.tvPosition.visibility = View.GONE
                binding.tvPositionIcon.visibility = View.GONE
            }
            if (it.department.isNotEmpty()) {
                binding.tvDepartment.text = it.department
                binding.tvDepartment.visibility = View.VISIBLE
                binding.tvDepartmentIcon.visibility = View.VISIBLE
            } else {
                binding.tvDepartment.visibility = View.GONE
                binding.tvDepartmentIcon.visibility = View.GONE
            }
        }
    }

    override fun unbind(binding: ItemMessengerUserSelectionVerticalBinding) {
    }

    override fun getDefaultLayout(): Int {
        return R.layout.item_messenger_user_selection_vertical
    }
}
