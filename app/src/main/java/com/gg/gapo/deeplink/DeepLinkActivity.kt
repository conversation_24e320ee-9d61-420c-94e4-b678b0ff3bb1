package com.gg.gapo.deeplink

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.airbnb.deeplinkdispatch.BaseDeepLinkDelegate
import com.airbnb.deeplinkdispatch.DeepLinkHandler
import com.gg.gapo.core.ui.R
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.utilities.interfacee.DoNotShowFlashMessage
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.domain.model.FeatureModel
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.deeplink.interceptor.DeepLinkInterceptor
import com.gg.gapo.feature.ask.deeplink.AskQuestionDeepLinkModule
import com.gg.gapo.feature.auth.deeplink.AuthenticationDeepLinkModule
import com.gg.gapo.feature.billing.deeplink.BillingDeeplinkModule
import com.gg.gapo.feature.call.deeplink.CallDeepLinkModule
import com.gg.gapo.feature.feed.follow.deeplink.FeedFollowDeepLinkModule
import com.gg.gapo.feature.group.deeplink.GroupDeepLinkModule
import com.gg.gapo.feature.hashtag.deeplink.HashtagDeepLinkModule
import com.gg.gapo.feature.invitation.deeplink.InvitationWorkspaceDeepLinkModule
import com.gg.gapo.feature.livestream.deeplink.LiveStreamDeepLinkModule
import com.gg.gapo.feature.meet.deeplink.GoogleMeetDeepLinkModule
import com.gg.gapo.feature.notification.deeplink.NotificationDeepLinkModule
import com.gg.gapo.feature.photo.editor.deeplink.PhotoEditorDeeplinkModule
import com.gg.gapo.feature.post.creator.deeplink.PostCreatorDeepLinkModule
import com.gg.gapo.feature.post.deeplink.PostDetailsDeepLinkModule
import com.gg.gapo.feature.report.deeplink.ReportDeepLinkModule
import com.gg.gapo.feature.search.deeplink.SearchDeepLinkModule
import com.gg.gapo.feature.setting.deeplink.SettingDeepLinkModule
import com.gg.gapo.feature.sticker.deeplink.StickerDeepLinkModule
import com.gg.gapo.feature.survey.deeplink.SurveyDeepLinkModule
import com.gg.gapo.feature.user.UserDeepLinkModule
import com.gg.gapo.feature.v2.deeplink.ApprovalDeepLinkModule
import com.gg.gapo.feature.workspace.deeplink.WorkspaceDeepLinkModule
import com.gg.gapo.flutterx.deeplink.GapoFlutterDeepLinkModule
import com.gg.gapo.home.presentation.HomeActivity
import com.gg.gapo.livekit.call.deeplink.CallDeepLinkModuleV2
import com.gg.gapo.messenger.MessengerDeepLinkModule
import org.koin.android.ext.android.inject
import timber.log.Timber

@DeepLinkHandler(
    value = [
        AppDeepLinkModule::class,
        ApprovalDeepLinkModule::class,
        MessengerDeepLinkModule::class,
        LiveStreamDeepLinkModule::class,
        GroupDeepLinkModule::class,
        PostDetailsDeepLinkModule::class,
        UserDeepLinkModule::class,
        SettingDeepLinkModule::class,
        NotificationDeepLinkModule::class,
        AskQuestionDeepLinkModule::class,
        CallDeepLinkModule::class,
        CallDeepLinkModuleV2::class,
        AuthenticationDeepLinkModule::class,
        StickerDeepLinkModule::class,
        PostCreatorDeepLinkModule::class,
        SurveyDeepLinkModule::class,
        GoogleMeetDeepLinkModule::class,
        FeedFollowDeepLinkModule::class,
        WorkspaceDeepLinkModule::class,
        InvitationWorkspaceDeepLinkModule::class,
        SearchDeepLinkModule::class,
        GapoFlutterDeepLinkModule::class,
        PhotoEditorDeeplinkModule::class,
        ReportDeepLinkModule::class,
        HashtagDeepLinkModule::class,
        BillingDeeplinkModule::class
    ]
)
class DeepLinkActivity : Activity(), DoNotShowFlashMessage {

    private val deepLinkInterceptor by inject<DeepLinkInterceptor>()

    private val deepLinkDelegate by inject<BaseDeepLinkDelegate>()

    private val workspaceManager by inject<WorkspaceManager>()

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent) {
        try {
            val isTaskRoot = isTaskRoot
            val interceptResult = deepLinkInterceptor.intercept(intent, isTaskRoot)
            val isDeepLinkEnabled = validateDeepLink(intent)
            if (isDeepLinkEnabled) {
                if (interceptResult.acceptDispatch) {
                    if (deepLinkInterceptor.shouldAddParentActivity(intent, isTaskRoot)) {
                        startActivity(Intent(this, HomeActivity::class.java))
                    }
                    if (interceptResult.nextClazz != null) {
                        startActivity(interceptResult.toIntent(this))
                    } else {
                        val result = deepLinkDelegate.dispatchFrom(this)
                        Timber.e("deepLinkDelegate = $result")
                    }
                } else if (interceptResult.nextClazz != null) {
                    startActivity(interceptResult.toIntent(this))
                }
            } else {
                showToastDeeplinkInvalid()
                if (isTaskRoot) {
                    startActivity(Intent(this@DeepLinkActivity, HomeActivity::class.java))
                } else {
                    finish()
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
        }
        finish()
    }

    private fun showToastDeeplinkInvalid() {
        GapoToast.makeNegative(
            this,
            GapoGlobalResources.getString(R.string.toggle_feature_unable_to_access_the_feature_description)
        ).show()
    }

    private fun validateDeepLink(intent: Intent): Boolean {
        val uri = intent.data.toString()
        val features = workspaceManager.currentWorkspace?.features ?: FeatureModel.DEFAULT
        val isDeepLinkEnabled = when {
            uri.contains("group") -> {
                if (uri.contains("/posts/") || uri.contains("/announcement")) {
                    true
                } else {
                    features.isEnable(Feature.GROUP)
                }
            }

            uri.contains("call") || uri.contains("call_v2") -> {
                features.isEnable(Feature.CALL)
            }

            uri.contains("calendar") || (uri.contains("collab") && uri.contains("meet")) -> {
                features.isEnable(Feature.CALENDAR)
            }

            uri.contains("task") || uri.contains("collab") -> {
                features.isEnable(Feature.TASK)
            }

            uri.contains("ticket") -> {
                features.isEnable(Feature.TICKET)
            }

            uri.contains("coin") -> {
                features.isEnable(Feature.REWARD_POINTS)
            }

            uri.contains("messenger") ||
                uri.contains("collab") ||
                uri.contains("subthread") ||
                uri.contains("chat") -> {
                features.isEnable(Feature.MESSENGER)
            }

            uri.contains("timekeeping") -> {
                features.isEnable(Feature.TIME_KEEPING)
            }

            uri.contains("survey") -> {
                features.isEnable(Feature.SURVEY)
            }

            uri.contains("approval") -> {
                features.isEnable(Feature.APPROVAL)
            }

            uri.contains("join-ws") -> {
                features.isEnable(Feature.INVITE_MEMBER_WORKSPACE)
            }

            uri.contains("ask") || uri.contains("my-question") -> {
                features.isEnable(Feature.ASK_ME)
            }

            else -> true
        }
        return isDeepLinkEnabled
    }
}
